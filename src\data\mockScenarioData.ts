// Column definitions for the scenario table
export const ScenarioTableColumns = {
  POLICY_YEAR: 'Policy Year',
  END_OF_AGE: 'End of Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_OUTLAY: 'Net Outlay',
  NET_SURRENDER_VALUE: 'Net Surrender Value',
  NET_DEATH_BENEFIT: 'Net Death Benefit'
} as const;

// Column configurations with additional display properties
export const ScenarioTableColumnConfig = [
  { 
    key: 'policyYear',
    header: ScenarioTableColumns.POLICY_YEAR,
    width: 100
  },
  { 
    key: 'endOfAge',
    header: ScenarioTableColumns.END_OF_AGE,
    width: 100
  },
  { 
    key: 'plannedPremium',
    header: ScenarioTableColumns.PLANNED_PREMIUM,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netOutlay',
    header: ScenarioTableColumns.NET_OUTLAY,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netSurrenderValue',
    header: ScenarioTableColumns.NET_SURRENDER_VALUE,
    width: 180,
    isCurrency: true
  },
  { 
    key: 'netDeathBenefit',
    header: ScenarioTableColumns.NET_DEATH_BENEFIT,
    width: 180,
    isCurrency: true
  }
];

export interface ScenarioTableData {
  policyYear: number;
  endOfAge: number;
  plannedPremium: number;
  netOutlay: number;
  netSurrenderValue: number;
  netDeathBenefit: number;
}

// ===== CASH VALUE ANALYSIS TABLE =====
export const As_Is_Loan = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const AsIsLoan = [
  { key: 'policyYear', header: As_Is_Loan.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: As_Is_Loan.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: As_Is_Loan.AGE, width: 80 },
  { key: 'plannedPremium', header: As_Is_Loan.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: As_Is_Loan.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: As_Is_Loan.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: As_Is_Loan.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: As_Is_Loan.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: As_Is_Loan.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: As_Is_Loan.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: As_Is_Loan.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: As_Is_Loan.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: As_Is_Loan.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: As_Is_Loan.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: As_Is_Loan.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: As_Is_Loan.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface AsIsLoanTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

// ===== DEATH BENEFIT ANALYSIS TABLE =====
export const Face_Amount = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const FaceAmount = [
  { key: 'policyYear', header: Face_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Face_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Face_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Face_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Face_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Face_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Face_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Face_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Face_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Face_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Face_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Face_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Face_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Face_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Face_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Face_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface FaceAmountTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

// ===== POLICY PERFORMANCE TABLE =====
export const Face_Amount_Varies_By_Year = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const FaceAmountVariesByYear = [
  { key: 'policyYear', header: Face_Amount_Varies_By_Year.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Face_Amount_Varies_By_Year.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Face_Amount_Varies_By_Year.AGE, width: 80 },
  { key: 'plannedPremium', header: Face_Amount_Varies_By_Year.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Face_Amount_Varies_By_Year.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Face_Amount_Varies_By_Year.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Face_Amount_Varies_By_Year.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Face_Amount_Varies_By_Year.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Face_Amount_Varies_By_Year.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Face_Amount_Varies_By_Year.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Face_Amount_Varies_By_Year.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Face_Amount_Varies_By_Year.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Face_Amount_Varies_By_Year.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Face_Amount_Varies_By_Year.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Face_Amount_Varies_By_Year.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Face_Amount_Varies_By_Year.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface FaceAmountVariesByYearTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

// ===== RISK ASSESSMENT TABLE =====
export const Premium_Amount = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const PremiumAmount = [
  { key: 'policyYear', header: Premium_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Premium_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Premium_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Premium_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Premium_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Premium_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Premium_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Premium_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Premium_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Premium_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Premium_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Premium_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Premium_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Premium_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Premium_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Premium_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface PremiumAmountTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

export const Stop_Premium_Amount = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const StopPremiumAmount = [
  { key: 'policyYear', header: Stop_Premium_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Stop_Premium_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Stop_Premium_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Stop_Premium_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Stop_Premium_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Stop_Premium_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Stop_Premium_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Stop_Premium_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Stop_Premium_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Stop_Premium_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Stop_Premium_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Stop_Premium_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Stop_Premium_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Stop_Premium_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Stop_Premium_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Stop_Premium_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface StopPremiumAmountTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

export const generateMockTableData = (scenario: any): ScenarioTableData[] => {
  console.log('📊 Generating table data for scenario:', scenario.id, scenario.name);

  // Generate mock table data for 10 years (2025-2034)
  const tableData: ScenarioTableData[] = [
    {
      policyYear: 1,
      endOfAge: 40,
      plannedPremium: 10000,
      netOutlay: 5000,
      netSurrenderValue: 50000,
      netDeathBenefit: 250000
    },
    {
      policyYear: 2,
      endOfAge: 41,
      plannedPremium: 10000,
      netOutlay: 5500,
      netSurrenderValue: 51000,
      netDeathBenefit: 255000
    },
    {
      policyYear: 3,
      endOfAge: 42,
      plannedPremium: 10000,
      netOutlay: 6000,
      netSurrenderValue: 52000,
      netDeathBenefit: 260000
    },
    {
      policyYear: 4,
      endOfAge: 43,
      plannedPremium: 10000,
      netOutlay: 6500,
      netSurrenderValue: 53000,
      netDeathBenefit: 265000
    },
    {
      policyYear: 5,
      endOfAge: 44,
      plannedPremium: 10000,
      netOutlay: 7000,
      netSurrenderValue: 54000,
      netDeathBenefit: 270000
    },
    {
      policyYear: 6,
      endOfAge: 45,
      plannedPremium: 10000,
      netOutlay: 7500,
      netSurrenderValue: 55000,
      netDeathBenefit: 275000
    },
    {
      policyYear: 7,
      endOfAge: 46,
      plannedPremium: 10000,
      netOutlay: 8000,
      netSurrenderValue: 56000,
      netDeathBenefit: 280000
    },
    {
      policyYear: 8,
      endOfAge: 47,
      plannedPremium: 10000,
      netOutlay: 8500,
      netSurrenderValue: 57000,
      netDeathBenefit: 285000
    },
    {
      policyYear: 9,
      endOfAge: 48,
      plannedPremium: 10000,
      netOutlay: 9000,
      netSurrenderValue: 58000,
      netDeathBenefit: 290000
    },
    {
      policyYear: 10,
      endOfAge: 49,
      plannedPremium: 10000,
      netOutlay: 9500,
      netSurrenderValue: 59000,
      netDeathBenefit: 295000
    }
  ];

  console.log('✅ Generated table data:', tableData.length, 'rows');
  return tableData;
};

// ===== GENERATE MOCK DATA FOR ALL TABLES =====

export const generateAsIsLoanTableData = (): AsIsLoanTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 2500,
      netValueBeginningOfYear: 12500,
      interestRate: 0.03,
      interestAmount: 375,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 525,
      loanRepayment: 600,
      loanOutstanding: 10425,
      charges: 100,
      netCashValue: 12550
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 2500,
      netValueBeginningOfYear: 15050,
      interestRate: 0.03,
      interestAmount: 451.5,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600,
      loanOutstanding: 10346.25,
      charges: 100,
      netCashValue: 15174.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 2500,
      netValueBeginningOfYear: 17674.25,
      interestRate: 0.03,
      interestAmount: 530.23,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600,
      loanOutstanding: 10263.56,
      charges: 100,
      netCashValue: 17875.17
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 2500,
      netValueBeginningOfYear: 20375.17,
      interestRate: 0.03,
      interestAmount: 611.25,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600,
      loanOutstanding: 10176.74,
      charges: 100,
      netCashValue: 20655.24
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 2500,
      netValueBeginningOfYear: 23155.24,
      interestRate: 0.03,
      interestAmount: 694.66,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600,
      loanOutstanding: 10085.58,
      charges: 100,
      netCashValue: 23517.06
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 45,
      plannedPremium: 2500,
      netValueBeginningOfYear: 26017.06,
      interestRate: 0.03,
      interestAmount: 780.51,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600,
      loanOutstanding: 9989.86,
      charges: 100,
      netCashValue: 26463.30
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 46,
      plannedPremium: 2500,
      netValueBeginningOfYear: 28963.30,
      interestRate: 0.03,
      interestAmount: 868.90,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600,
      loanOutstanding: 9889.35,
      charges: 100,
      netCashValue: 29496.70
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 47,
      plannedPremium: 2500,
      netValueBeginningOfYear: 31996.70,
      interestRate: 0.03,
      interestAmount: 959.90,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600,
      loanOutstanding: 9783.82,
      charges: 100,
      netCashValue: 32620.13
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 48,
      plannedPremium: 2500,
      netValueBeginningOfYear: 35120.13,
      interestRate: 0.03,
      interestAmount: 1053.60,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600,
      loanOutstanding: 9673.01,
      charges: 100,
      netCashValue: 35836.55
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 49,
      plannedPremium: 2500,
      netValueBeginningOfYear: 38336.55,
      interestRate: 0.03,
      interestAmount: 1150.10,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 483.65,
      loanRepayment: 600,
      loanOutstanding: 9556.66,
      charges: 100,
      netCashValue: 39148.99
    }
  ];
};

export const generateFaceAmountTableData = (): FaceAmountTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 12500.00,
      interestRate: 0.03,
      interestAmount: 375.00,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.00,
      loanRepayment: 600.00,
      loanOutstanding: 10425.00,
      charges: 100.00,
      netCashValue: 12507.14
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 15007.14,
      interestRate: 0.03,
      interestAmount: 450.21,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.00,
      loanOutstanding: 10346.25,
      charges: 100.00,
      netCashValue: 15086.39
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 17586.39,
      interestRate: 0.03,
      interestAmount: 527.59,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.00,
      loanOutstanding: 10263.56,
      charges: 100.00,
      netCashValue: 17740.10
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 20240.10,
      interestRate: 0.03,
      interestAmount: 607.20,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600.00,
      loanOutstanding: 10176.74,
      charges: 100.00,
      netCashValue: 20470.70
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 22970.70,
      interestRate: 0.03,
      interestAmount: 689.12,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600.00,
      loanOutstanding: 10085.58,
      charges: 100.00,
      netCashValue: 23280.70
    }
  ];
};

export const generateFaceAmountVariesByYearTableData = (): FaceAmountVariesByYearTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 35,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 12500.0,
      interestRate: 0.03,
      interestAmount: 375.0,
      faceAmount: 200000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.0,
      loanRepayment: 600.0,
      loanOutstanding: 10425.0,
      charges: 100.0,
      netCashValue: 12580.0
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 36,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 15080.0,
      interestRate: 0.03,
      interestAmount: 452.4,
      faceAmount: 200000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.0,
      loanOutstanding: 10346.25,
      charges: 100.0,
      netCashValue: 15235.15
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 37,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 17735.15,
      interestRate: 0.03,
      interestAmount: 532.05,
      faceAmount: 250000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.0,
      loanOutstanding: 10263.56,
      charges: 100.0,
      netCashValue: 17967.89
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 38,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 20467.89,
      interestRate: 0.03,
      interestAmount: 614.04,
      faceAmount: 300000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600.0,
      loanOutstanding: 10176.74,
      charges: 100.0,
      netCashValue: 20780.75
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 39,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 23280.75,
      interestRate: 0.03,
      interestAmount: 698.42,
      faceAmount: 300000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600.0,
      loanOutstanding: 10085.58,
      charges: 100.0,
      netCashValue: 23088.35
    }
  ];
};

export const generatePremiumAmountTableData = (): PremiumAmountTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 12500.00,
      interestRate: 0.03,
      interestAmount: 375.00,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.00,
      loanRepayment: 600.00,
      loanOutstanding: 10425.00,
      charges: 100.00,
      netCashValue: 13550.00
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 17050.00,
      interestRate: 0.03,
      interestAmount: 511.50,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.00,
      loanOutstanding: 10346.25,
      charges: 100.00,
      netCashValue: 17234.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 20734.25,
      interestRate: 0.03,
      interestAmount: 622.03,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.00,
      loanOutstanding: 10263.56,
      charges: 100.00,
      netCashValue: 20938.97
    }
  ];
};

export const generateStopPremiumAmountTableData = (): StopPremiumAmountTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 12500.00,
      interestRate: 0.03,
      interestAmount: 375.00,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.00,
      loanRepayment: 600.00,
      loanOutstanding: 10425.00,
      charges: 100.00,
      netCashValue: 11550.00
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 11550.00,
      interestRate: 0.03,
      interestAmount: 346.50,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.00,
      loanOutstanding: 10346.25,
      charges: 100.00,
      netCashValue: 10975.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 10975.25,
      interestRate: 0.03,
      interestAmount: 329.26,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.00,
      loanOutstanding: 10263.56,
      charges: 100.00,
      netCashValue: 10387.20
    }
  ];
};
